package com.ideal.ieai.server.repository.topo.acttimecalculate;

import com.ideal.ieai.server.repository.topo.sequence.GanttBean;
import com.ideal.ieai.server.repository.topo.sequence.GanttDelayerBean;
import com.ideal.ieai.server.repository.topo.sequence.GanttValTimeBean;
import com.ideal.ieai.server.repository.topo.sequence.SequenceManager;
import com.ideal.ieai.server.repository.topo.sequence.SequenceTime;
import com.ideal.ieai.server.repository.topo.acttimeck.ActTimeCk;
import com.ideal.ieai.server.repository.topo.acttimeck.ActTimeModel;
import com.ideal.ieai.server.repository.avgtime.AvgTimeManager;
import com.ideal.ieai.server.repository.avgtime.AvgTimeList;
import com.ideal.ieai.server.common.ConfigReader;
import com.ideal.ieai.server.common.Environment;
import com.ideal.ieai.server.common.ServerEnv;
import com.ideal.ieai.server.repository.topo.topoabschluss.TopoAbschlussManager;
import org.apache.log4j.Logger;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Enhanced Activity Time Calculate Manager
 * ???????????????????????????????????????????????????
 * 
 * ????????
 * 1. ????????????????????????????????????????????????????
 * 2. ???????????F???????????
 * 3. ??????????????????????????????????????????????????????
 * 4. ????????????????????????????????????
 */
public class EnhancedActTimeCalculateManager {
    
    private static final Logger _log = Logger.getLogger(EnhancedActTimeCalculateManager.class);
    private ActTimeCalculateManager originalManager = new ActTimeCalculateManager();
    
    /**
     * ????????
     */
    private static class NodeDependencyGraph {
        private Map<String, GanttBean> allNodes = new HashMap<>();
        private Map<String, List<String>> parentDependencies = new HashMap<>();
        private Map<String, List<String>> childDependencies = new HashMap<>();
        private Set<String> crossProjectNodes = new HashSet<>();
        
        public void addNode(GanttBean node) {
            allNodes.put(node.getId(), node);
            if ("1".equals(node.getIsProOut())) {
                crossProjectNodes.add(node.getId());
            }
        }
        
        public void addDependency(String childId, String parentId) {
            parentDependencies.computeIfAbsent(childId, k -> new ArrayList<>()).add(parentId);
            childDependencies.computeIfAbsent(parentId, k -> new ArrayList<>()).add(childId);
        }
        
        public GanttBean getNode(String nodeId) {
            return allNodes.get(nodeId);
        }
        
        public List<String> getParents(String nodeId) {
            return parentDependencies.getOrDefault(nodeId, new ArrayList<>());
        }
        
        public List<String> getChildren(String nodeId) {
            return childDependencies.getOrDefault(nodeId, new ArrayList<>());
        }
        
        public Set<String> getAllNodeIds() {
            return allNodes.keySet();
        }
        
        public boolean isCrossProjectNode(String nodeId) {
            return crossProjectNodes.contains(nodeId);
        }
    }
    
    /**
     * ?????????????????
     * ???????????????????????????????????????????
     */
    public void calculateActTime(int i, String dataDate, List actList, List actWarnList, 
                               long nowTime, Connection con) {
        try {
            long a0 = System.currentTimeMillis();
            
            // ????????????????
            ConfigReader cf = ConfigReader.getInstance();
            cf.init();
            boolean s = Boolean.parseBoolean(cf.getProperties(Environment.TOPO_YEAR_LAST_SWITCH, "false"));
            _log.info("??????????:" + dataDate + " topo.year.last.switch=" + s);
            
            if (s) {
                if (i == 0 && SequenceManager.getInstance().getAbsActCountByDateDate(dataDate, con) != 0) {
                    TopoAbschlussManager.getInstance().insertSubCommand(dataDate, 0l, con);
                } else {
                    TopoAbschlussManager.getInstance().insertSubCommand(dataDate, 1l, con);
                }
            }
            
            long fl = originalManager.getGantFlagByDatadate();
            long exists = SequenceManager.getInstance().getActCountByDateDate(dataDate, con);
            
            if (exists != 0) {
                fl = 0;
                List<GanttBean> list = SequenceManager.getInstance().updateBeforeActTopoInstanceList(dataDate, 0l, con);
                SequenceManager.getInstance().updateGantData(dataDate, fl, con);
                SequenceManager.getInstance().updateActTopoInstanceData(dataDate, 0l, con, list);
            } else {
                SequenceManager.getInstance().updateGantData(dataDate, 1l, con);
            }
            
            // ???????????????
            boolean formapSwitch = ServerEnv.getInstance().getBooleanConfigNew2(Environment.QL_OVER_TIME_FORMAP_SWITCH, false);
            if (formapSwitch) {
                SequenceManager.getInstance().getRuleOvertimebyDate(dataDate, con);
            }
            
            con.commit();
            long a1 = System.currentTimeMillis();
            _log.info("????????:" + dataDate + " proc==" + (a1 - a0));
            
            // ?????????????
            List avgTimeList = new ArrayList();
            boolean avgTimeWarnningSwitch = ServerEnv.getInstance().getBooleanConfig("avgTimeWarnningSwitch", false);
            if (avgTimeWarnningSwitch) {
                avgTimeList = AvgTimeManager.getInstance().getAllAvgTimeByNullDayWeekMonth();
            }
            
            // ???????????????????
            Map mapAct = calculateEnhancedPredictedTimes(dataDate, nowTime, con, avgTimeList);
            
            // ??????????????????
            Map tmpMap = new HashMap();
            tmpMap.put("mapAct", mapAct);
            tmpMap.put("dataDate", dataDate);
            actList.add(tmpMap);
            
            Map tmpMap1 = new HashMap();
            tmpMap1.put("realRunTime", 0L);
            tmpMap1.put("nowTime", nowTime);
            tmpMap1.put("realTime", 0L);
            tmpMap1.put("mapAct", mapAct);
            tmpMap1.put("dataDate", dataDate);
            actWarnList.add(tmpMap1);
            
            long a2 = System.currentTimeMillis();
            _log.info("????????:" + dataDate + "  ????=" + (a2 - a1));
            
        } catch (Exception e) {
            _log.error("????????:" + dataDate, e);
        }
    }
    
    /**
     * ????????????????
     * 1. ?????????????
     * 2. ????????????
     * 3. ??????????????????
     * 4. ????????????
     */
    private Map calculateEnhancedPredictedTimes(String dataDate, long nowTime, Connection con, List avgTimeList) 
            throws Exception {
        
        _log.info("?????????????????????????: " + dataDate);
        
        // ?????????????????
        NodeDependencyGraph graph = buildDependencyGraph(dataDate, con);
        
        // ?????????????????????????
        Map<String, GanttBean> calculatedNodes = new HashMap<>();
        calculatePredictedTimesTopologically(graph, dataDate, nowTime, con, avgTimeList, calculatedNodes);
        
        _log.info("????????????????????????????: " + calculatedNodes.size());
        return calculatedNodes;
    }

    /**
     * ???????????????
     */
    private NodeDependencyGraph buildDependencyGraph(String dataDate, Connection con) throws Exception {
        NodeDependencyGraph graph = new NodeDependencyGraph();

        // ??????н??
        List<GanttBean> allNodes = new ArrayList<>();
        SequenceManager.getInstance().getUUIDMainAct(dataDate, allNodes, con);

        _log.info("????????????: " + allNodes.size());

        // ??????н?????
        for (GanttBean node : allNodes) {
            // ?????????????
            GanttBean detailedNode = enrichNodeInformation(node, dataDate, con);
            graph.addNode(detailedNode);
        }

        // ???????????
        for (GanttBean node : allNodes) {
            List<String> parentIds = new ArrayList<>();

            // ?????????????
            SequenceManager.getInstance().getParentId(node, parentIds, dataDate, con);

            // ???????????????
            SequenceManager.getInstance().getProOutParentId(node, parentIds, con);

            // ???????????????
            for (String parentId : parentIds) {
                graph.addDependency(node.getId(), parentId);
            }
        }

        _log.info("??????????????");
        return graph;
    }

    /**
     * ??????????????????????????????
     */
    private GanttBean enrichNodeInformation(GanttBean node, String dataDate, Connection con) throws Exception {
        // ?????????估?????????
        GanttValTimeBean ganttValTimeBean = SequenceManager.getInstance().getActValTime(node, con);
        GanttDelayerBean ganttDelayerBean = SequenceManager.getInstance().getActDelayer(node, con);

        // ????????????
        List<SequenceTime> srtlist = SequenceManager.getInstance().getActRealRunTimeTwo(node, con);
        long realRunTime = 0;
        long realTime = 0;

        for (SequenceTime st : srtlist) {
            if (st.getType() == 0) {
                realTime = st.getTime();
            } else {
                realRunTime = st.getTime();
            }
        }

        if (srtlist.isEmpty()) {
            realRunTime = 0;
        }

        // У?????????ó????????????????????????????
        ActTimeCk atc = new ActTimeCk();
        ActTimeModel atm = atc.queryActTimeCkCal(con, node.getProjectName(), node.getActName(), dataDate);

        // ????Щ????洢???????????????
        node.setRunTime(String.valueOf(realRunTime));

        return node;
    }

    /**
     * 按拓扑顺序计算预计时间
     */
    private void calculatePredictedTimesTopologically(NodeDependencyGraph graph, String dataDate,
                                                    long nowTime, Connection con, List avgTimeList,
                                                    Map<String, GanttBean> calculatedNodes) throws Exception {

        Set<String> visited = new HashSet<>();
        Set<String> calculating = new HashSet<>();

        // 对所有节点进行拓扑排序计算
        for (String nodeId : graph.getAllNodeIds()) {
            if (!visited.contains(nodeId)) {
                calculateNodeRecursively(nodeId, graph, dataDate, nowTime, con, avgTimeList,
                                       calculatedNodes, visited, calculating);
            }
        }
    }

    /**
     * 递归计算单个节点的预计时间
     */
    private void calculateNodeRecursively(String nodeId, NodeDependencyGraph graph, String dataDate,
                                        long nowTime, Connection con, List avgTimeList,
                                        Map<String, GanttBean> calculatedNodes,
                                        Set<String> visited, Set<String> calculating) throws Exception {

        if (visited.contains(nodeId)) {
            return;
        }

        if (calculating.contains(nodeId)) {
            _log.warn("检测到循环依赖，节点ID: " + nodeId);
            return;
        }

        calculating.add(nodeId);

        GanttBean currentNode = graph.getNode(nodeId);
        if (currentNode == null) {
            _log.warn("节点不存在: " + nodeId);
            calculating.remove(nodeId);
            return;
        }

        // 首先计算所有父节点
        List<String> parentIds = graph.getParents(nodeId);
        for (String parentId : parentIds) {
            calculateNodeRecursively(parentId, graph, dataDate, nowTime, con, avgTimeList,
                                   calculatedNodes, visited, calculating);
        }

        // 计算当前节点的预计时间
        calculateSingleNodePredictedTime(currentNode, parentIds, graph, dataDate, nowTime, con, avgTimeList);

        // 将计算结果存储
        calculatedNodes.put(nodeId, currentNode);
        visited.add(nodeId);
        calculating.remove(nodeId);
    }

    /**
     * 计算单个节点的预计开始时间和预计结束时间
     * 核心改进：确保节点的预计开始时间是父级节点最大的预计结束时间
     */
    private void calculateSingleNodePredictedTime(GanttBean currentNode, List<String> parentIds,
                                                NodeDependencyGraph graph, String dataDate,
                                                long nowTime, Connection con, List avgTimeList) throws Exception {

        // 计算父节点的最大预计结束时间
        long maxParentEndTime = calculateMaxParentEndTime(parentIds, graph, nowTime);

        // 获取节点的基本信息
        long realRunTime = Long.parseLong(currentNode.getRunTime() == null ? "0" : currentNode.getRunTime());

        // 获取校准时间信息
        ActTimeCk atc = new ActTimeCk();
        ActTimeModel atm = atc.queryActTimeCkCal(con, currentNode.getProjectName(),
                                               currentNode.getActName(), dataDate);

        // 根据节点状态计算预计时间
        if (currentNode.getActState() == null || "1".equals(currentNode.getActState())) {
            // 未开始状态：计算预计开始时间和预计结束时间
            calculatePredictedTimesForPendingNode(currentNode, maxParentEndTime, realRunTime, atm, dataDate);

        } else if ("0".equals(currentNode.getActState())) {
            // 运行状态：预计结束时间基于实际开始时间
            calculatePredictedTimesForRunningNode(currentNode, realRunTime, atm, nowTime);

        } else if ("2".equals(currentNode.getActState()) || "3".equals(currentNode.getActState())) {
            // 完成状态：清空预计时间
            currentNode.setExpStartTime(null);
            currentNode.setExpEndTime(null);
        }

        // 处理跨项目节点的特殊逻辑
        if ("1".equals(currentNode.getIsProOut())) {
            handleCrossProjectNodeTime(currentNode, maxParentEndTime, realRunTime, atm);
        }
    }
